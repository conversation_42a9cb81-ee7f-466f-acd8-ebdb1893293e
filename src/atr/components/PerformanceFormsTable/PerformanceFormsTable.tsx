import { FC, useState, useCallback } from 'react';

import { usePerformanceFormsContext, PerformanceFormModel } from 'atr/domain';

import { PerformanceDataTable } from '@/shared/components';
import { DEFAULT_PAGE_SIZES } from '@/shared/constants/pagination';

import {
  DeleteFormModal,
  PerformanceFormsEmpty,
  PerformanceFormsError
} from './components';
import { useColumnGroups, useColumns } from './hooks';

export interface PerformanceFormsTableProps {
  emptyScreenSubtitle?: string;
  className?: string;
}

export const PerformanceFormsTable: FC<PerformanceFormsTableProps> = ({
  className
}) => {
  const { queryResult, dataSource, onSetPageSize } =
    usePerformanceFormsContext();

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedForm, setSelectedForm] = useState<PerformanceFormModel | null>(
    null
  );

  const handleDeleteForm = useCallback((form: PerformanceFormModel) => {
    setSelectedForm(form);
    setIsDeleteModalOpen(true);
  }, []);

  const handleCloseDeleteModal = useCallback(() => {
    setIsDeleteModalOpen(false);
    setSelectedForm(null);
  }, []);

  const columns = useColumns({ onDeleteForm: handleDeleteForm });
  const columnGroups = useColumnGroups();

  return (
    <>
      <div data-attributes="PerformanceFormsTable" className={className}>
        <PerformanceDataTable
          dataAttributes="PerformanceFormsTable"
          {...dataSource}
          showContainer
          styles={{
            minHeight: !queryResult.data?.items.length ? 440 : 0
          }}
          queryResult={queryResult}
          columns={columns}
          columnGroups={columnGroups}
          pageSizes={DEFAULT_PAGE_SIZES}
          setPageSize={onSetPageSize}
          renderNoResults={PerformanceFormsEmpty}
          renderError={PerformanceFormsError}
          loaderText="ATR Forms are being loaded"
          paginationSize="24"
        />
      </div>

      {isDeleteModalOpen && (
        <DeleteFormModal
          isOpen={isDeleteModalOpen}
          form={selectedForm}
          onClose={handleCloseDeleteModal}
          onSuccess={handleCloseDeleteModal}
        />
      )}
    </>
  );
};
