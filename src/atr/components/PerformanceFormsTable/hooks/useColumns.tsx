import { useMemo } from 'react';

import { DataColumnProps } from '@epam/uui-core';
import {
  ContextMenuTrigger,
  ContextualMenu,
  ContextualMenuList,
  ContextualMenuListItem,
  IconButton,
  TableUserInfoCell,
  Tag
} from '@ot/onetalent-ui-kit';
import compact from 'lodash/compact';

import { ColumnGroup } from '@/atr/components/PerformanceFormsTable/hooks/useColumnGroups';
import {
  ATR_FORM_DELETABLE_MAJOR_STATUSES,
  PerformanceFormModel
} from '@/atr/domain';
import { CellContent } from '@/shared/components';
import { Drawer } from '@/shared/constants';
import { useToggleVisibility } from '@/shared/hooks';
import { AdminFeatureFlag, FeatureFlag } from '@/shared/modules/featureFlags';
import { formatDateToDateTime } from '@/shared/utils';

import { getTagVariantByMajorStatus } from '../utils';

enum Column {
  AtrFormId = 'AtrFormId',
  AtrTemplateName = 'AtrTemplateName',
  AtrFormMajorStatus = 'AtrFormMajorStatus',
  AtrFormMinorStatus = 'AtrFormMinorStatus',
  EmployeeID = 'EmployeeID',
  EmployeeFullName = 'EmployeeFullName',
  EmployeeCompanyName = 'EmployeeCompanyName',
  EmployeeDirectorate = 'EmployeeDirectorate',
  EmployeeFunction = 'EmployeeFunction',
  EmployeeDivision = 'EmployeeDivision',
  ATRGroupName = 'ATRGroupName',
  AssessmentLineManagerID = 'AssessmentLineManagerID',
  AssessmentLineManagerFullName = 'AssessmentLineManagerFullName',
  AssessmentB2BManagerID = 'AssessmentB2BManagerID',
  AssessmentB2BManagerFullName = 'AssessmentB2BManagerFullName',
  DottedLineManagerID = 'DottedLineManagerID',
  DottedLineManagerFullName = 'DottedLineManagerFullName',
  LastUpdated = 'LastUpdated',
  UpdatedBy = 'UpdatedBy',
  Menu = 'Menu'
}

const getColumnDisplayName = (columnName: Column) => {
  switch (columnName) {
    case Column.AtrTemplateName:
    case Column.ATRGroupName:
      return 'Name';
    case Column.AtrFormMajorStatus:
      return 'Major';
    case Column.AtrFormMinorStatus:
      return 'Minor';
    case Column.EmployeeCompanyName:
      return 'Company';
    case Column.EmployeeDirectorate:
      return 'Directorate';
    case Column.EmployeeFunction:
      return 'Function';
    case Column.EmployeeDivision:
      return 'Division';
    case Column.AtrFormId:
    case Column.EmployeeID:
    case Column.AssessmentLineManagerID:
    case Column.AssessmentB2BManagerID:
    case Column.DottedLineManagerID:
      return 'ID';
    case Column.EmployeeFullName:
    case Column.AssessmentLineManagerFullName:
    case Column.AssessmentB2BManagerFullName:
    case Column.DottedLineManagerFullName:
      return 'Full Name';
    case Column.LastUpdated:
      return 'Time & Date';
    case Column.UpdatedBy:
      return 'User Name';
    default:
      return '';
  }
};

export interface UseColumnsProps {
  onDeleteForm?: (form: PerformanceFormModel) => void;
}

export const useColumns = (
  props: UseColumnsProps = {}
): DataColumnProps<PerformanceFormModel>[] => {
  const { onDeleteForm } = props;
  const { toggleDrawer } = useToggleVisibility();

  return useMemo(
    () =>
      compact([
        {
          key: Column.AtrFormId,
          caption: getColumnDisplayName(Column.AtrFormId),
          group: ColumnGroup.AtrForm,
          width: 120,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.id,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
            >
              {form.id}
            </CellContent>
          )
        },
        {
          key: Column.AtrTemplateName,
          caption: getColumnDisplayName(Column.AtrTemplateName),
          group: ColumnGroup.AtrTemplate,
          width: 280,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.templateName,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
            >
              {form.templateName}
            </CellContent>
          )
        },
        {
          key: Column.AtrFormMajorStatus,
          caption: getColumnDisplayName(Column.AtrFormMajorStatus),
          group: ColumnGroup.AtrFormStatus,
          width: 200,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.majorStatus,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
            >
              <Tag variant={getTagVariantByMajorStatus(form.majorStatus)}>
                {form.majorStatus}
              </Tag>
            </CellContent>
          )
        },
        {
          key: Column.AtrFormMinorStatus,
          caption: getColumnDisplayName(Column.AtrFormMinorStatus),
          group: ColumnGroup.AtrFormStatus,
          width: 160,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.minorStatus,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
              classNames="line-clamp-2"
            >
              {form.minorStatus}
            </CellContent>
          )
        },
        {
          key: Column.EmployeeID,
          caption: getColumnDisplayName(Column.EmployeeID),
          group: ColumnGroup.Employee,
          width: 120,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.employee.employeeId,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
            >
              {form.employee.employeeId}
            </CellContent>
          )
        },
        {
          key: Column.EmployeeFullName,
          caption: getColumnDisplayName(Column.EmployeeFullName),
          group: ColumnGroup.Employee,
          width: 200,
          render: (form) => (
            <CellContent>
              {form.employee.fullNameEnglish ? (
                <TableUserInfoCell
                  size="Small"
                  tooltipMode="always"
                  className="px-0"
                  email={form.employee.email}
                  fullName={form.employee.fullNameEnglish}
                  company={form.employee.companyName}
                  position={form.employee.positionName}
                />
              ) : (
                ''
              )}
            </CellContent>
          )
        },
        {
          key: Column.EmployeeCompanyName,
          caption: getColumnDisplayName(Column.EmployeeCompanyName),
          group: ColumnGroup.Employee,
          width: 180,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.employeeCompanyName,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
              classNames="line-clamp-2"
            >
              {form.employeeCompanyName}
            </CellContent>
          )
        },
        {
          key: Column.EmployeeDirectorate,
          caption: getColumnDisplayName(Column.EmployeeDirectorate),
          group: ColumnGroup.Employee,
          width: 180,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.employeeDirectorate,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
              classNames="line-clamp-2"
            >
              {form.employeeDirectorate}
            </CellContent>
          )
        },
        {
          key: Column.EmployeeFunction,
          caption: getColumnDisplayName(Column.EmployeeFunction),
          group: ColumnGroup.Employee,
          width: 180,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.employeeFunction,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
              classNames="line-clamp-2"
            >
              {form.employeeFunction}
            </CellContent>
          )
        },
        {
          key: Column.EmployeeDivision,
          caption: getColumnDisplayName(Column.EmployeeDivision),
          group: ColumnGroup.Employee,
          width: 180,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.employeeDivision,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
              classNames="line-clamp-2"
            >
              {form.employeeDivision}
            </CellContent>
          )
        },
        {
          key: Column.ATRGroupName,
          caption: getColumnDisplayName(Column.ATRGroupName),
          group: ColumnGroup.ATRGroup,
          width: 284,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.atrGroupName,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
              classNames="line-clamp-2"
            >
              {form.atrGroupName}
            </CellContent>
          )
        },
        {
          key: Column.AssessmentLineManagerID,
          caption: getColumnDisplayName(Column.AssessmentLineManagerID),
          group: ColumnGroup.AssessmentManagerLM,
          width: 120,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.assessmentLineManager.employeeId,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
            >
              {form.assessmentLineManager.employeeId}
            </CellContent>
          )
        },
        {
          key: Column.AssessmentLineManagerFullName,
          caption: getColumnDisplayName(Column.AssessmentLineManagerFullName),
          group: ColumnGroup.AssessmentManagerLM,
          width: 200,
          render: (form) => (
            <CellContent>
              {form.assessmentLineManager.fullNameEnglish ? (
                <TableUserInfoCell
                  size="Small"
                  tooltipMode="always"
                  className="px-0"
                  email={form.assessmentLineManager.email}
                  fullName={form.assessmentLineManager.fullNameEnglish}
                  company={form.assessmentLineManager.companyName}
                  position={form.assessmentLineManager.positionName}
                />
              ) : (
                ''
              )}
            </CellContent>
          )
        },
        {
          key: Column.AssessmentB2BManagerID,
          caption: getColumnDisplayName(Column.AssessmentB2BManagerID),
          group: ColumnGroup.AssessmentManagerB2B,
          width: 120,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.assessmentB2BManager?.employeeId,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
            >
              {form.assessmentB2BManager?.employeeId}
            </CellContent>
          )
        },
        {
          key: Column.AssessmentB2BManagerFullName,
          caption: getColumnDisplayName(Column.AssessmentB2BManagerFullName),
          group: ColumnGroup.AssessmentManagerB2B,
          width: 200,
          render: (form) => (
            <CellContent>
              {form.assessmentB2BManager?.fullNameEnglish ? (
                <TableUserInfoCell
                  size="Small"
                  tooltipMode="always"
                  className="px-0"
                  email={form.assessmentB2BManager.email}
                  fullName={form.assessmentB2BManager.fullNameEnglish}
                  company={form.assessmentB2BManager.companyName}
                  position={form.assessmentB2BManager.positionName}
                />
              ) : (
                ''
              )}
            </CellContent>
          )
        },
        {
          key: Column.DottedLineManagerID,
          caption: getColumnDisplayName(Column.DottedLineManagerID),
          group: ColumnGroup.DottedLineManager,
          width: 120,
          render: (form) => (
            <CellContent
              tooltipProps={{
                title: form.dottedLineManager?.employeeId,
                className:
                  '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
              }}
            >
              {form.dottedLineManager?.employeeId}
            </CellContent>
          )
        },
        {
          key: Column.DottedLineManagerFullName,
          caption: getColumnDisplayName(Column.DottedLineManagerFullName),
          group: ColumnGroup.DottedLineManager,
          width: 200,
          render: (form) => (
            <CellContent>
              {form.dottedLineManager?.fullNameEnglish ? (
                <TableUserInfoCell
                  size="Small"
                  tooltipMode="always"
                  className="px-0"
                  email={form.dottedLineManager.email}
                  fullName={form.dottedLineManager.fullNameEnglish}
                  company={form.dottedLineManager.companyName}
                  position={form.dottedLineManager.positionName}
                />
              ) : (
                ''
              )}
            </CellContent>
          )
        },
        {
          key: Column.LastUpdated,
          caption: getColumnDisplayName(Column.LastUpdated),
          group: ColumnGroup.LastUpdated,
          width: 120,
          render: (form) => {
            const formattedDate = form.lastUpdated
              ? formatDateToDateTime(form.lastUpdated)
              : '';
            return (
              <CellContent
                tooltipProps={{
                  title: formattedDate,
                  className:
                    '!rounded-4 !max-w-[220px] !px-8 !py-4 !text-body-4-regular'
                }}
                classNames="line-clamp-2"
              >
                {formattedDate}
              </CellContent>
            );
          }
        },
        {
          key: Column.UpdatedBy,
          caption: getColumnDisplayName(Column.UpdatedBy),
          group: ColumnGroup.UpdatedBy,
          width: 200,
          render: (form) => (
            <CellContent>
              {form.updatedBy ? (
                <TableUserInfoCell
                  size="Small"
                  tooltipMode="always"
                  className="px-0"
                  email={form.updatedBy.email}
                  fullName={form.updatedBy.fullNameEnglish}
                  company={form.updatedBy.companyName}
                  position={form.updatedBy.positionName}
                />
              ) : (
                ''
              )}
            </CellContent>
          )
        },
        {
          key: Column.Menu,
          width: 72,
          minWidth: 72,
          fix: 'right',
          render: (form) => {
            const canDeleteForm = ATR_FORM_DELETABLE_MAJOR_STATUSES.includes(
              form.majorStatus as (typeof ATR_FORM_DELETABLE_MAJOR_STATUSES)[number]
            );
            return (
              <CellContent>
                <ContextualMenu dataAttributes="ContextualMenu">
                  <ContextMenuTrigger>
                    <IconButton icon="Kebab" variant="Tertiary" />
                  </ContextMenuTrigger>
                  <ContextualMenuList>
                    <FeatureFlag name={AdminFeatureFlag.AdminAtrAuditLog}>
                      <ContextualMenuListItem
                        onClick={() => {
                          toggleDrawer({
                            name: Drawer.AuditLogDrawer,
                            performanceFormId: form.id
                          });
                        }}
                      >
                        Audit Log
                      </ContextualMenuListItem>
                    </FeatureFlag>

                    <ContextualMenuListItem
                      disabled={!canDeleteForm}
                      onClick={() => {
                        if (canDeleteForm) {
                          onDeleteForm?.(form);
                        }
                      }}
                      tooltip={{
                        title: canDeleteForm
                          ? null
                          : 'You cannot delete a form that has passed the Normalization step'
                      }}
                    >
                      Delete Form
                    </ContextualMenuListItem>
                  </ContextualMenuList>
                </ContextualMenu>
              </CellContent>
            );
          }
        }
      ]),
    [toggleDrawer, onDeleteForm]
  );
};
